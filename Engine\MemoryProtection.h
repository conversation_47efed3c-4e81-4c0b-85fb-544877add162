#ifndef __MEMORY_PROTECTION_H
#define __MEMORY_PROTECTION_H

#include <Windows.h>
#include <Psapi.h>
#include <TlHelp32.h>
#include <vector>
#include <string>
#include <set>
#include <map>
#include <thread>
#include <atomic>

// Memory protection constants
#define MEMORY_PROTECTION_ENABLED 1
#define MAX_SUSPICIOUS_HANDLES 10
#define MEMORY_CHECK_INTERVAL 10000  // 10 seconds
#define HANDLE_CHECK_INTERVAL 15000  // 15 seconds (reduced frequency to be less aggressive)

// Configuration flags to reduce false positives
#define ENABLE_DEBUGGER_DETECTION 1     // Set to 0 to disable debugger detection (reduces false positives)
#define ENABLE_HANDLE_MONITORING 1      // Set to 0 to disable handle monitoring (TEMPORARY for debugging)
#define ENABLE_MEMORY_INTEGRITY 1      // Set to 0 to disable memory integrity checks (TEMPORARY for debugging)

// Suspicious access rights that indicate memory editing attempts
#define SUSPICIOUS_ACCESS_RIGHTS (PROCESS_VM_READ | PROCESS_VM_WRITE | \
                                 PROCESS_QUERY_INFORMATION | PROCESS_CREATE_THREAD | \
                                 PROCESS_SET_INFORMATION | PROCESS_SUSPEND_RESUME)

// Structure to track process handle information
struct ProcessHandleInfo {
    DWORD processId;
    std::string processName;
    std::string processPath;
    DWORD accessRights;
    SYSTEMTIME detectionTime;
    bool isWhitelisted;
    
    ProcessHandleInfo() : processId(0), accessRights(0), isWhitelisted(false) {
        GetSystemTime(&detectionTime);
    }
};

// Structure for memory region protection
struct ProtectedMemoryRegion {
    LPVOID baseAddress;
    SIZE_T size;
    DWORD originalProtection;
    DWORD currentProtection;
    std::string regionName;
    bool isActive;
    
    ProtectedMemoryRegion() : baseAddress(nullptr), size(0), originalProtection(0), 
                             currentProtection(0), isActive(false) {}
};

// Memory protection class
class MemoryProtection {
public:
    // Public static members that need to be accessed by external functions
    static std::vector<ProtectedMemoryRegion> m_protectedRegions;

    // Public methods
    static void TriggerSecurityResponse(const std::string& reason);

private:
    static std::atomic<bool> m_bProtectionActive;
    static std::atomic<bool> m_bShutdownRequested;
    static std::vector<ProcessHandleInfo> m_suspiciousHandles;
    static std::set<std::string> m_whitelistedProcesses;
    static std::set<std::string> m_blacklistedProcesses;
    static std::thread m_handleMonitorThread;
    static std::thread m_memoryIntegrityThread;

    // Internal methods
    static bool IsProcessWhitelisted(const std::string& processName);
    static bool IsProcessBlacklisted(const std::string& processName);
    static std::string GetProcessNameFromPID(DWORD processId);
    static std::string GetProcessPathFromPID(DWORD processId);
    static bool HasSuspiciousAccessRights(DWORD accessRights);
    static void LogSuspiciousActivity(const ProcessHandleInfo& handleInfo);

    // Thread functions
    static void HandleMonitorThreadFunc();
    static void MemoryIntegrityThreadFunc();

public:
    // Initialization and cleanup
    static bool Initialize();
    static void Shutdown();
    
    // Handle monitoring
    static bool StartHandleMonitoring();
    static void StopHandleMonitoring();
    static bool DetectSuspiciousHandles();
    static std::vector<ProcessHandleInfo> GetSuspiciousHandles();
    
    // Memory protection
    static bool ProtectMemoryRegion(LPVOID baseAddress, SIZE_T size, const std::string& regionName);
    static bool UnprotectMemoryRegion(LPVOID baseAddress);
    static bool CheckMemoryIntegrity();
    static bool SetupGuardPages();
    
    // Process detection
    static bool DetectMemoryEditingTools();
    static bool DetectDebuggers();
    static bool DetectVirtualMachines();
    
    // Configuration
    static void AddWhitelistedProcess(const std::string& processName);
    static void AddBlacklistedProcess(const std::string& processName);
    static void RemoveWhitelistedProcess(const std::string& processName);
    
    // Status and reporting
    static bool IsProtectionActive();
    static void GenerateSecurityReport();
    static void ClearSuspiciousHandles();

    // Exception handler (needs access to protected members)
    static LONG WINAPI MemoryAccessViolationHandler(EXCEPTION_POINTERS* exceptionInfo);
};

// Exception handler is now a static member function of MemoryProtection class

// Anti-debugging functions
bool IsDebuggerPresent_Advanced();
bool IsRemoteDebuggerPresent_Advanced();
bool CheckForHardwareBreakpoints();
bool CheckForSoftwareBreakpoints();

// Memory editing tool detection
bool DetectCheatEngine();
bool DetectProcessHacker();
bool DetectMemorySharp();
bool Detectx64dbg();
bool DetectOllyDbg();
bool DetectIDA();
bool DetectWinAPIOverride();

// System integrity checks
bool CheckSystemIntegrity();
bool DetectHooks();
bool ValidateModuleIntegrity();

// Utility functions
std::string GetLastErrorString();
void SecureZeroMemoryEx(void* ptr, size_t size);
bool IsRunningInVirtualMachine();
bool IsSuspiciousProcessPattern(const std::string& processName, DWORD processId);

// Global protection state
extern volatile bool g_bMemoryProtectionEnabled;
extern volatile bool g_bSecurityViolationDetected;

#endif // __MEMORY_PROTECTION_H
