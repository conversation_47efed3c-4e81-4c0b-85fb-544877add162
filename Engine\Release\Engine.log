﻿  Engine.cpp
G:\Kal\New Project\Cleaning Sources\New Packet Protection\Engine\Engine\VButtonFix.h(130,16): warning C4244: 'initializing': conversion from 'double' to 'float', possible loss of data
  (compiling source file 'Engine.cpp')
  
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\xutility(4829,18): warning C4244: '=': conversion from 'wchar_t' to 'char', possible loss of data
  (compiling source file 'Engine.cpp')
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\xutility(4829,18):
      the template instantiation context (the oldest one first) is
          G:\Kal\New Project\Cleaning Sources\New Packet Protection\Engine\Engine\Engine.cpp(928,19):
          see reference to function template instantiation 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<std::_String_iterator<std::_String_val<std::_Simple_types<_Elem>>>,0>(_Iter,_Iter,const _Alloc &)' being compiled
          with
          [
              _Elem=wchar_t,
              _Iter=std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t>>>,
              _Alloc=std::allocator<char>
          ]
              G:\Kal\New Project\Cleaning Sources\New Packet Protection\Engine\Engine\Engine.cpp(928,19):
              see the first reference to 'std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string' in 'GetWinBuild'
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\xstring(788,17):
          see reference to function template instantiation 'void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct_from_iter<wchar_t*,wchar_t*,_Size_type>(_Iter,const _Sent,_Size)' being compiled
          with
          [
              _Size_type=unsigned int,
              _Iter=wchar_t *,
              _Sent=wchar_t *,
              _Size=unsigned int
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\xstring(944,18):
          see reference to function template instantiation '_OutIt *std::_Copy_n_unchecked4<wchar_t*,_Size,char*>(_InIt,_SizeTy,_OutIt)' being compiled
          with
          [
              _OutIt=char *,
              _Size=unsigned int,
              _InIt=wchar_t *,
              _SizeTy=unsigned int
          ]
  
  MemoryProtection.cpp
G:\Kal\New Project\Cleaning Sources\New Packet Protection\Engine\Engine\MemoryProtection.cpp(1269,5): error C3861: 'DetourTransactionBegin': identifier not found
G:\Kal\New Project\Cleaning Sources\New Packet Protection\Engine\Engine\MemoryProtection.cpp(1270,5): error C3861: 'DetourUpdateThread': identifier not found
G:\Kal\New Project\Cleaning Sources\New Packet Protection\Engine\Engine\MemoryProtection.cpp(1272,5): error C3861: 'DetourAttach': identifier not found
G:\Kal\New Project\Cleaning Sources\New Packet Protection\Engine\Engine\MemoryProtection.cpp(1273,5): error C3861: 'DetourAttach': identifier not found
G:\Kal\New Project\Cleaning Sources\New Packet Protection\Engine\Engine\MemoryProtection.cpp(1274,5): error C3861: 'DetourAttach': identifier not found
G:\Kal\New Project\Cleaning Sources\New Packet Protection\Engine\Engine\MemoryProtection.cpp(1275,5): error C3861: 'DetourAttach': identifier not found
G:\Kal\New Project\Cleaning Sources\New Packet Protection\Engine\Engine\MemoryProtection.cpp(1277,19): error C3861: 'DetourTransactionCommit': identifier not found
G:\Kal\New Project\Cleaning Sources\New Packet Protection\Engine\Engine\MemoryProtection.cpp(1291,9): error C3861: 'DetourTransactionBegin': identifier not found
G:\Kal\New Project\Cleaning Sources\New Packet Protection\Engine\Engine\MemoryProtection.cpp(1292,9): error C3861: 'DetourUpdateThread': identifier not found
G:\Kal\New Project\Cleaning Sources\New Packet Protection\Engine\Engine\MemoryProtection.cpp(1294,9): error C3861: 'DetourDetach': identifier not found
G:\Kal\New Project\Cleaning Sources\New Packet Protection\Engine\Engine\MemoryProtection.cpp(1295,9): error C3861: 'DetourDetach': identifier not found
G:\Kal\New Project\Cleaning Sources\New Packet Protection\Engine\Engine\MemoryProtection.cpp(1296,9): error C3861: 'DetourDetach': identifier not found
G:\Kal\New Project\Cleaning Sources\New Packet Protection\Engine\Engine\MemoryProtection.cpp(1297,9): error C3861: 'DetourDetach': identifier not found
G:\Kal\New Project\Cleaning Sources\New Packet Protection\Engine\Engine\MemoryProtection.cpp(1299,9): error C3861: 'DetourTransactionCommit': identifier not found
