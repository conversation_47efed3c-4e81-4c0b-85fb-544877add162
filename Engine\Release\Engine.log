﻿  MemoryProtection.cpp
G:\Kal\New Project\Cleaning Sources\New Packet Protection\Engine\Engine\MemoryProtection.cpp(1213,39): error C2248: 'MemoryProtection::IsProcessWhitelisted': cannot access private member declared in class 'MemoryProtection'
      G:\Kal\New Project\Cleaning Sources\New Packet Protection\Engine\Engine\MemoryProtection.cpp(346,24):
      see declaration of 'MemoryProtection::IsProcessWhitelisted'
      G:\Kal\New Project\Cleaning Sources\New Packet Protection\Engine\Engine\MemoryProtection.h(61,7):
      see declaration of 'MemoryProtection'
  
G:\Kal\New Project\Cleaning Sources\New Packet Protection\Engine\Engine\MemoryProtection.cpp(1293,35): error C2248: 'MemoryProtection::IsProcessWhitelisted': cannot access private member declared in class 'MemoryProtection'
      G:\Kal\New Project\Cleaning Sources\New Packet Protection\Engine\Engine\MemoryProtection.cpp(346,24):
      see declaration of 'MemoryProtection::IsProcessWhitelisted'
      G:\Kal\New Project\Cleaning Sources\New Packet Protection\Engine\Engine\MemoryProtection.h(61,7):
      see declaration of 'MemoryProtection'
  
G:\Kal\New Project\Cleaning Sources\New Packet Protection\Engine\Engine\MemoryProtection.cpp(1333,35): error C2248: 'MemoryProtection::IsProcessWhitelisted': cannot access private member declared in class 'MemoryProtection'
      G:\Kal\New Project\Cleaning Sources\New Packet Protection\Engine\Engine\MemoryProtection.cpp(346,24):
      see declaration of 'MemoryProtection::IsProcessWhitelisted'
      G:\Kal\New Project\Cleaning Sources\New Packet Protection\Engine\Engine\MemoryProtection.h(61,7):
      see declaration of 'MemoryProtection'
  
