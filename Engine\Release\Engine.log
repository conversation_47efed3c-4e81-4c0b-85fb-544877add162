﻿  MemoryProtection.cpp
G:\Kal\New Project\Cleaning Sources\New Packet Protection\Engine\Engine\MemoryProtection.cpp(410,53): error C2248: 'MemoryProtection::m_protectedRegions': cannot access private member declared in class 'MemoryProtection'
      G:\Kal\New Project\Cleaning Sources\New Packet Protection\Engine\Engine\MemoryProtection.h(58,47):
      see declaration of 'MemoryProtection::m_protectedRegions'
      G:\Kal\New Project\Cleaning Sources\New Packet Protection\Engine\Engine\MemoryProtection.h(53,7):
      see declaration of 'MemoryProtection'
  
G:\Kal\New Project\Cleaning Sources\New Packet Protection\Engine\Engine\MemoryProtection.cpp(415,35): error C2248: 'MemoryProtection::TriggerSecurityResponse': cannot access private member declared in class 'MemoryProtection'
      G:\Kal\New Project\Cleaning Sources\New Packet Protection\Engine\Engine\MemoryProtection.cpp(329,24):
      see declaration of 'MemoryProtection::TriggerSecurityResponse'
      G:\Kal\New Project\Cleaning Sources\New Packet Protection\Engine\Engine\MemoryProtection.h(53,7):
      see declaration of 'MemoryProtection'
  
