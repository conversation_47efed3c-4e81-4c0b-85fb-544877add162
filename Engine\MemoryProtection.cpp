#include "MemoryProtection.h"
#include "Variables.h"
#include <iostream>
#include <sstream>
#include <algorithm>
#include <mutex>

// Global variables
volatile bool g_bMemoryProtectionEnabled = false;
volatile bool g_bSecurityViolationDetected = false;

// Static member initialization
std::atomic<bool> MemoryProtection::m_bProtectionActive(false);
std::atomic<bool> MemoryProtection::m_bShutdownRequested(false);
std::vector<ProcessHandleInfo> MemoryProtection::m_suspiciousHandles;
std::vector<ProtectedMemoryRegion> MemoryProtection::m_protectedRegions;
std::set<std::string> MemoryProtection::m_whitelistedProcesses;
std::set<std::string> MemoryProtection::m_blacklistedProcesses;
std::thread MemoryProtection::m_handleMonitorThread;
std::thread MemoryProtection::m_memoryIntegrityThread;

static std::mutex g_protectionMutex;

bool MemoryProtection::Initialize() {
    std::lock_guard<std::mutex> lock(g_protectionMutex);
    
    if (m_bProtectionActive.load()) {
        return true; // Already initialized
    }
    
    // Initialize whitelisted processes (system processes that legitimately access memory)
    m_whitelistedProcesses.insert("system");
    m_whitelistedProcesses.insert("csrss.exe");
    m_whitelistedProcesses.insert("winlogon.exe");
    m_whitelistedProcesses.insert("services.exe");
    m_whitelistedProcesses.insert("lsass.exe");
    m_whitelistedProcesses.insert("svchost.exe");
    m_whitelistedProcesses.insert("explorer.exe");
    m_whitelistedProcesses.insert("dwm.exe");
    m_whitelistedProcesses.insert("conhost.exe");
    
    // Initialize blacklisted processes (known memory editing tools)
    m_blacklistedProcesses.insert("cheatengine-x86_64.exe");
    m_blacklistedProcesses.insert("cheatengine-i386.exe");
    m_blacklistedProcesses.insert("cheatengine.exe");
    m_blacklistedProcesses.insert("processhacker.exe");
    m_blacklistedProcesses.insert("x64dbg.exe");
    m_blacklistedProcesses.insert("x32dbg.exe");
    m_blacklistedProcesses.insert("ollydbg.exe");
    m_blacklistedProcesses.insert("ida.exe");
    m_blacklistedProcesses.insert("ida64.exe");
    m_blacklistedProcesses.insert("windbg.exe");
    m_blacklistedProcesses.insert("memorysharp.exe");
    m_blacklistedProcesses.insert("artmoney.exe");
    m_blacklistedProcesses.insert("gameguardian.exe");
    m_blacklistedProcesses.insert("speedhack.exe");
    m_blacklistedProcesses.insert("tsearch.exe");
    
    // Set up exception handler for memory access violations
    SetUnhandledExceptionFilter(MemoryAccessViolationHandler);
    
    // Start monitoring threads
    m_bShutdownRequested.store(false);
    m_handleMonitorThread = std::thread(HandleMonitorThreadFunc);
    m_memoryIntegrityThread = std::thread(MemoryIntegrityThreadFunc);
    
    m_bProtectionActive.store(true);
    g_bMemoryProtectionEnabled = true;
    
    return true;
}

void MemoryProtection::Shutdown() {
    std::lock_guard<std::mutex> lock(g_protectionMutex);
    
    if (!m_bProtectionActive.load()) {
        return; // Already shut down
    }
    
    m_bShutdownRequested.store(true);
    
    // Wait for threads to finish
    if (m_handleMonitorThread.joinable()) {
        m_handleMonitorThread.join();
    }
    if (m_memoryIntegrityThread.joinable()) {
        m_memoryIntegrityThread.join();
    }
    
    // Unprotect all memory regions
    for (auto& region : m_protectedRegions) {
        if (region.isActive) {
            UnprotectMemoryRegion(region.baseAddress);
        }
    }
    m_protectedRegions.clear();
    
    m_bProtectionActive.store(false);
    g_bMemoryProtectionEnabled = false;
}

bool MemoryProtection::DetectSuspiciousHandles() {
    HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (hSnapshot == INVALID_HANDLE_VALUE) {
        return false;
    }
    
    PROCESSENTRY32 pe32;
    pe32.dwSize = sizeof(PROCESSENTRY32);
    
    bool suspiciousActivityDetected = false;
    DWORD currentProcessId = GetCurrentProcessId();
    
    if (Process32First(hSnapshot, &pe32)) {
        do {
            if (pe32.th32ProcessID == currentProcessId) {
                continue; // Skip our own process
            }
            
            // Check if process is blacklisted
            std::string processName = pe32.szExeFile;
            std::transform(processName.begin(), processName.end(), processName.begin(), ::tolower);
            
            if (IsProcessBlacklisted(processName)) {
                ProcessHandleInfo handleInfo;
                handleInfo.processId = pe32.th32ProcessID;
                handleInfo.processName = processName;
                handleInfo.processPath = GetProcessPathFromPID(pe32.th32ProcessID);
                handleInfo.accessRights = SUSPICIOUS_ACCESS_RIGHTS;
                handleInfo.isWhitelisted = false;
                
                m_suspiciousHandles.push_back(handleInfo);
                LogSuspiciousActivity(handleInfo);
                suspiciousActivityDetected = true;
                
                // Immediate response for blacklisted processes
                TriggerSecurityResponse("Blacklisted memory editing tool detected: " + processName);
                continue;
            }
            
            // Try to open handle to check access rights
            HANDLE hProcess = OpenProcess(PROCESS_QUERY_INFORMATION, FALSE, pe32.th32ProcessID);
            if (hProcess != NULL) {
                // Check if this process has a handle to our process
                HANDLE hCurrentProcess = OpenProcess(SUSPICIOUS_ACCESS_RIGHTS, FALSE, currentProcessId);
                if (hCurrentProcess != NULL) {
                    CloseHandle(hCurrentProcess);
                    
                    if (!IsProcessWhitelisted(processName)) {
                        ProcessHandleInfo handleInfo;
                        handleInfo.processId = pe32.th32ProcessID;
                        handleInfo.processName = processName;
                        handleInfo.processPath = GetProcessPathFromPID(pe32.th32ProcessID);
                        handleInfo.accessRights = SUSPICIOUS_ACCESS_RIGHTS;
                        handleInfo.isWhitelisted = false;
                        
                        m_suspiciousHandles.push_back(handleInfo);
                        LogSuspiciousActivity(handleInfo);
                        suspiciousActivityDetected = true;
                    }
                }
                CloseHandle(hProcess);
            }
            
        } while (Process32Next(hSnapshot, &pe32));
    }
    
    CloseHandle(hSnapshot);
    return suspiciousActivityDetected;
}

bool MemoryProtection::DetectMemoryEditingTools() {
    bool detected = false;
    
    // Check for specific memory editing tools
    if (DetectCheatEngine()) {
        TriggerSecurityResponse("Cheat Engine detected");
        detected = true;
    }
    
    if (DetectProcessHacker()) {
        TriggerSecurityResponse("Process Hacker detected");
        detected = true;
    }
    
    if (DetectMemorySharp()) {
        TriggerSecurityResponse("MemorySharp detected");
        detected = true;
    }
    
    if (Detectx64dbg()) {
        TriggerSecurityResponse("x64dbg detected");
        detected = true;
    }
    
    if (DetectOllyDbg()) {
        TriggerSecurityResponse("OllyDbg detected");
        detected = true;
    }
    
    if (DetectIDA()) {
        TriggerSecurityResponse("IDA Pro detected");
        detected = true;
    }
    
    return detected;
}

void MemoryProtection::HandleMonitorThreadFunc() {
    while (!m_bShutdownRequested.load()) {
        try {
            if (DetectSuspiciousHandles()) {
                g_bSecurityViolationDetected = true;
            }
            
            if (DetectMemoryEditingTools()) {
                g_bSecurityViolationDetected = true;
            }
            
            // Clean up old suspicious handle entries (older than 5 minutes)
            SYSTEMTIME currentTime;
            GetSystemTime(&currentTime);
            
            m_suspiciousHandles.erase(
                std::remove_if(m_suspiciousHandles.begin(), m_suspiciousHandles.end(),
                    [&currentTime](const ProcessHandleInfo& info) {
                        FILETIME currentFT, detectionFT;
                        SystemTimeToFileTime(&currentTime, &currentFT);
                        SystemTimeToFileTime(&info.detectionTime, &detectionFT);
                        
                        ULARGE_INTEGER current, detection;
                        current.LowPart = currentFT.dwLowDateTime;
                        current.HighPart = currentFT.dwHighDateTime;
                        detection.LowPart = detectionFT.dwLowDateTime;
                        detection.HighPart = detectionFT.dwHighDateTime;
                        
                        // Remove entries older than 5 minutes (300 seconds)
                        return (current.QuadPart - detection.QuadPart) > (300ULL * 10000000ULL);
                    }),
                m_suspiciousHandles.end()
            );
            
        } catch (...) {
            // Handle any exceptions to prevent thread termination
        }
        
        Sleep(HANDLE_CHECK_INTERVAL);
    }
}

void MemoryProtection::MemoryIntegrityThreadFunc() {
    while (!m_bShutdownRequested.load()) {
        try {
            if (!CheckMemoryIntegrity()) {
                TriggerSecurityResponse("Memory integrity violation detected");
                g_bSecurityViolationDetected = true;
            }
            
            if (IsDebuggerPresent_Advanced()) {
                TriggerSecurityResponse("Debugger detected");
                g_bSecurityViolationDetected = true;
            }
            
        } catch (...) {
            // Handle any exceptions to prevent thread termination
        }
        
        Sleep(MEMORY_CHECK_INTERVAL);
    }
}

// Utility function implementations
bool MemoryProtection::IsProcessWhitelisted(const std::string& processName) {
    std::string lowerName = processName;
    std::transform(lowerName.begin(), lowerName.end(), lowerName.begin(), ::tolower);
    return m_whitelistedProcesses.find(lowerName) != m_whitelistedProcesses.end();
}

bool MemoryProtection::IsProcessBlacklisted(const std::string& processName) {
    std::string lowerName = processName;
    std::transform(lowerName.begin(), lowerName.end(), lowerName.begin(), ::tolower);
    return m_blacklistedProcesses.find(lowerName) != m_blacklistedProcesses.end();
}

std::string MemoryProtection::GetProcessNameFromPID(DWORD processId) {
    HANDLE hProcess = OpenProcess(PROCESS_QUERY_INFORMATION | PROCESS_VM_READ, FALSE, processId);
    if (hProcess == NULL) {
        return "Unknown";
    }

    char processName[MAX_PATH];
    if (GetModuleBaseNameA(hProcess, NULL, processName, sizeof(processName))) {
        CloseHandle(hProcess);
        return std::string(processName);
    }

    CloseHandle(hProcess);
    return "Unknown";
}

std::string MemoryProtection::GetProcessPathFromPID(DWORD processId) {
    HANDLE hProcess = OpenProcess(PROCESS_QUERY_INFORMATION | PROCESS_VM_READ, FALSE, processId);
    if (hProcess == NULL) {
        return "Unknown";
    }

    char processPath[MAX_PATH];
    if (GetModuleFileNameExA(hProcess, NULL, processPath, sizeof(processPath))) {
        CloseHandle(hProcess);
        return std::string(processPath);
    }

    CloseHandle(hProcess);
    return "Unknown";
}

void MemoryProtection::LogSuspiciousActivity(const ProcessHandleInfo& handleInfo) {
    // Log to debug output or file
    std::ostringstream logMessage;
    logMessage << "[SECURITY] Suspicious process detected: "
               << "PID=" << handleInfo.processId
               << ", Name=" << handleInfo.processName
               << ", Path=" << handleInfo.processPath
               << ", AccessRights=0x" << std::hex << handleInfo.accessRights;

    OutputDebugStringA(logMessage.str().c_str());
}

void MemoryProtection::TriggerSecurityResponse(const std::string& reason) {
    // Set global flag
    g_bSecurityViolationDetected = true;

    // Log the security violation
    std::string logMessage = "[SECURITY VIOLATION] " + reason;
    OutputDebugStringA(logMessage.c_str());

    // Show message to user and terminate
    std::string message = "Security violation detected: " + reason +
                         "\n\nThe application will now terminate to protect Project 404 integrity.";
    MessageBoxA(NULL, message.c_str(), "Project 404 - Security Alert", MB_OK | MB_ICONERROR);

    // Set the global stop flag and exit
    extern bool StopClient;
    StopClient = true;
    exit(1);
}

bool MemoryProtection::CheckMemoryIntegrity() {
    // Check if any protected memory regions have been modified
    for (const auto& region : m_protectedRegions) {
        if (!region.isActive) continue;

        MEMORY_BASIC_INFORMATION mbi;
        if (VirtualQuery(region.baseAddress, &mbi, sizeof(mbi)) == 0) {
            return false; // Failed to query memory
        }

        // Check if protection has been changed
        if (mbi.Protect != region.currentProtection) {
            return false; // Memory protection has been modified
        }
    }

    return true;
}

bool MemoryProtection::ProtectMemoryRegion(LPVOID baseAddress, SIZE_T size, const std::string& regionName) {
    DWORD oldProtection;
    if (!VirtualProtect(baseAddress, size, PAGE_READONLY, &oldProtection)) {
        return false;
    }

    ProtectedMemoryRegion region;
    region.baseAddress = baseAddress;
    region.size = size;
    region.originalProtection = oldProtection;
    region.currentProtection = PAGE_READONLY;
    region.regionName = regionName;
    region.isActive = true;

    m_protectedRegions.push_back(region);
    return true;
}

bool MemoryProtection::UnprotectMemoryRegion(LPVOID baseAddress) {
    for (auto& region : m_protectedRegions) {
        if (region.baseAddress == baseAddress && region.isActive) {
            DWORD oldProtection;
            if (VirtualProtect(baseAddress, region.size, region.originalProtection, &oldProtection)) {
                region.isActive = false;
                return true;
            }
            return false;
        }
    }
    return false;
}

// Exception handler for memory access violations
LONG WINAPI MemoryAccessViolationHandler(EXCEPTION_POINTERS* exceptionInfo) {
    if (exceptionInfo->ExceptionRecord->ExceptionCode == EXCEPTION_ACCESS_VIOLATION) {
        // Log the access violation
        ULONG_PTR faultAddress = exceptionInfo->ExceptionRecord->ExceptionInformation[1];
        std::ostringstream logMessage;
        logMessage << "[SECURITY] Memory access violation at address: 0x"
                   << std::hex << faultAddress;
        OutputDebugStringA(logMessage.str().c_str());

        // Check if this is an attempt to access protected memory
        for (const auto& region : MemoryProtection::m_protectedRegions) {
            if (region.isActive &&
                faultAddress >= (ULONG_PTR)region.baseAddress &&
                faultAddress < (ULONG_PTR)region.baseAddress + region.size) {

                MemoryProtection::TriggerSecurityResponse("Unauthorized memory access detected");
                return EXCEPTION_EXECUTE_HANDLER;
            }
        }
    }

    return EXCEPTION_CONTINUE_SEARCH;
}

// Memory editing tool detection functions
bool DetectCheatEngine() {
    // Check for Cheat Engine windows
    HWND hWnd = FindWindowA("TMainForm", NULL);
    if (hWnd != NULL) {
        char windowTitle[256];
        GetWindowTextA(hWnd, windowTitle, sizeof(windowTitle));
        std::string title = windowTitle;
        std::transform(title.begin(), title.end(), title.begin(), ::tolower);
        if (title.find("cheat engine") != std::string::npos) {
            return true;
        }
    }

    // Check for Cheat Engine processes
    HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (hSnapshot != INVALID_HANDLE_VALUE) {
        PROCESSENTRY32 pe32;
        pe32.dwSize = sizeof(PROCESSENTRY32);

        if (Process32First(hSnapshot, &pe32)) {
            do {
                std::string processName = pe32.szExeFile;
                std::transform(processName.begin(), processName.end(), processName.begin(), ::tolower);

                if (processName.find("cheatengine") != std::string::npos ||
                    processName.find("cheat engine") != std::string::npos ||
                    processName == "cheatengine.exe" ||
                    processName == "cheatengine-x86_64.exe" ||
                    processName == "cheatengine-i386.exe") {
                    CloseHandle(hSnapshot);
                    return true;
                }
            } while (Process32Next(hSnapshot, &pe32));
        }
        CloseHandle(hSnapshot);
    }

    return false;
}

bool DetectProcessHacker() {
    // Check for Process Hacker window
    HWND hWnd = FindWindowA("ProcessHacker", NULL);
    if (hWnd != NULL) return true;

    // Check for Process Hacker process
    HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (hSnapshot != INVALID_HANDLE_VALUE) {
        PROCESSENTRY32 pe32;
        pe32.dwSize = sizeof(PROCESSENTRY32);

        if (Process32First(hSnapshot, &pe32)) {
            do {
                std::string processName = pe32.szExeFile;
                std::transform(processName.begin(), processName.end(), processName.begin(), ::tolower);

                if (processName.find("processhacker") != std::string::npos ||
                    processName == "processhacker.exe") {
                    CloseHandle(hSnapshot);
                    return true;
                }
            } while (Process32Next(hSnapshot, &pe32));
        }
        CloseHandle(hSnapshot);
    }

    return false;
}

bool DetectMemorySharp() {
    // MemorySharp is a .NET library, so check for .NET processes that might be using it
    HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (hSnapshot != INVALID_HANDLE_VALUE) {
        PROCESSENTRY32 pe32;
        pe32.dwSize = sizeof(PROCESSENTRY32);

        if (Process32First(hSnapshot, &pe32)) {
            do {
                std::string processName = pe32.szExeFile;
                std::transform(processName.begin(), processName.end(), processName.begin(), ::tolower);

                // Check for common MemorySharp-based tools
                if (processName.find("memorysharp") != std::string::npos ||
                    processName.find("memoryhacker") != std::string::npos ||
                    processName.find("memoryeditor") != std::string::npos) {
                    CloseHandle(hSnapshot);
                    return true;
                }

                // Check for suspicious .NET executables
                if (processName.find(".vshost.exe") != std::string::npos) {
                    // This could be a Visual Studio hosted process running MemorySharp
                    HANDLE hProcess = OpenProcess(PROCESS_QUERY_INFORMATION | PROCESS_VM_READ, FALSE, pe32.th32ProcessID);
                    if (hProcess != NULL) {
                        // Check if it has handles to our process
                        HANDLE hCurrentProcess = OpenProcess(PROCESS_VM_READ | PROCESS_VM_WRITE, FALSE, GetCurrentProcessId());
                        if (hCurrentProcess != NULL) {
                            CloseHandle(hCurrentProcess);
                            CloseHandle(hProcess);
                            CloseHandle(hSnapshot);
                            return true;
                        }
                        CloseHandle(hProcess);
                    }
                }
            } while (Process32Next(hSnapshot, &pe32));
        }
        CloseHandle(hSnapshot);
    }

    return false;
}

bool Detectx64dbg() {
    // Check for x64dbg/x32dbg windows
    HWND hWnd = FindWindowA("Qt5QWindowIcon", NULL);
    if (hWnd != NULL) {
        char windowTitle[256];
        GetWindowTextA(hWnd, windowTitle, sizeof(windowTitle));
        std::string title = windowTitle;
        std::transform(title.begin(), title.end(), title.begin(), ::tolower);
        if (title.find("x64dbg") != std::string::npos || title.find("x32dbg") != std::string::npos) {
            return true;
        }
    }

    // Check for x64dbg processes
    HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (hSnapshot != INVALID_HANDLE_VALUE) {
        PROCESSENTRY32 pe32;
        pe32.dwSize = sizeof(PROCESSENTRY32);

        if (Process32First(hSnapshot, &pe32)) {
            do {
                std::string processName = pe32.szExeFile;
                std::transform(processName.begin(), processName.end(), processName.begin(), ::tolower);

                if (processName == "x64dbg.exe" || processName == "x32dbg.exe") {
                    CloseHandle(hSnapshot);
                    return true;
                }
            } while (Process32Next(hSnapshot, &pe32));
        }
        CloseHandle(hSnapshot);
    }

    return false;
}

bool DetectOllyDbg() {
    // Check for OllyDbg window
    HWND hWnd = FindWindowA("OllyDbg", NULL);
    if (hWnd != NULL) return true;

    // Check for OllyDbg process
    HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (hSnapshot != INVALID_HANDLE_VALUE) {
        PROCESSENTRY32 pe32;
        pe32.dwSize = sizeof(PROCESSENTRY32);

        if (Process32First(hSnapshot, &pe32)) {
            do {
                std::string processName = pe32.szExeFile;
                std::transform(processName.begin(), processName.end(), processName.begin(), ::tolower);

                if (processName.find("ollydbg") != std::string::npos) {
                    CloseHandle(hSnapshot);
                    return true;
                }
            } while (Process32Next(hSnapshot, &pe32));
        }
        CloseHandle(hSnapshot);
    }

    return false;
}

bool DetectIDA() {
    // Check for IDA Pro processes
    HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (hSnapshot != INVALID_HANDLE_VALUE) {
        PROCESSENTRY32 pe32;
        pe32.dwSize = sizeof(PROCESSENTRY32);

        if (Process32First(hSnapshot, &pe32)) {
            do {
                std::string processName = pe32.szExeFile;
                std::transform(processName.begin(), processName.end(), processName.begin(), ::tolower);

                if (processName == "ida.exe" || processName == "ida64.exe" ||
                    processName == "idaw.exe" || processName == "idaw64.exe") {
                    CloseHandle(hSnapshot);
                    return true;
                }
            } while (Process32Next(hSnapshot, &pe32));
        }
        CloseHandle(hSnapshot);
    }

    return false;
}

// Advanced anti-debugging functions
bool IsDebuggerPresent_Advanced() {
    // Standard Windows API check
    if (IsDebuggerPresent()) {
        return true;
    }

    // Check PEB (Process Environment Block) manually
    __try {
        __asm {
            mov eax, fs:[30h]    // Get PEB address
            mov al, [eax + 2h]   // Get BeingDebugged flag
            test al, al
            jnz debugger_detected
        }

        // Check NtGlobalFlag in PEB
        __asm {
            mov eax, fs:[30h]    // Get PEB address
            mov eax, [eax + 68h] // Get NtGlobalFlag
            and eax, 70h         // Check for heap flags
            jnz debugger_detected
        }

        // Check heap flags
        __asm {
            mov eax, fs:[30h]    // Get PEB address
            mov eax, [eax + 18h] // Get ProcessHeap
            mov eax, [eax + 0Ch] // Get Flags
            and eax, 2           // Check HEAP_TAIL_CHECKING_ENABLED
            jnz debugger_detected
        }

        return false;

    debugger_detected:
        return true;
    }
    __except(EXCEPTION_EXECUTE_HANDLER) {
        return true; // Exception occurred, likely due to debugging
    }
}

bool IsRemoteDebuggerPresent_Advanced() {
    BOOL isRemoteDebuggerPresent = FALSE;

    // Use CheckRemoteDebuggerPresent API
    if (CheckRemoteDebuggerPresent(GetCurrentProcess(), &isRemoteDebuggerPresent)) {
        if (isRemoteDebuggerPresent) {
            return true;
        }
    }

    // Manual check using NtQueryInformationProcess
    typedef NTSTATUS (WINAPI *pNtQueryInformationProcess)(
        HANDLE ProcessHandle,
        DWORD ProcessInformationClass,
        PVOID ProcessInformation,
        ULONG ProcessInformationLength,
        PULONG ReturnLength
    );

    HMODULE hNtdll = GetModuleHandleA("ntdll.dll");
    if (hNtdll) {
        pNtQueryInformationProcess NtQueryInformationProcess =
            (pNtQueryInformationProcess)GetProcAddress(hNtdll, "NtQueryInformationProcess");

        if (NtQueryInformationProcess) {
            DWORD debugPort = 0;
            NTSTATUS status = NtQueryInformationProcess(
                GetCurrentProcess(),
                7, // ProcessDebugPort
                &debugPort,
                sizeof(debugPort),
                NULL
            );

            if (status == 0 && debugPort != 0) {
                return true;
            }
        }
    }

    return false;
}

bool CheckForHardwareBreakpoints() {
    CONTEXT ctx;
    ctx.ContextFlags = CONTEXT_DEBUG_REGISTERS;

    if (GetThreadContext(GetCurrentThread(), &ctx)) {
        // Check if any debug registers are set
        if (ctx.Dr0 != 0 || ctx.Dr1 != 0 || ctx.Dr2 != 0 || ctx.Dr3 != 0) {
            return true;
        }

        // Check Dr7 for breakpoint enables
        if ((ctx.Dr7 & 0xFF) != 0) {
            return true;
        }
    }

    return false;
}

bool CheckForSoftwareBreakpoints() {
    // Check for INT3 (0xCC) breakpoints in our code
    HMODULE hModule = GetModuleHandleA("Engine.dll");
    if (hModule == NULL) {
        return false;
    }

    MODULEINFO moduleInfo;
    if (!GetModuleInformation(GetCurrentProcess(), hModule, &moduleInfo, sizeof(moduleInfo))) {
        return false;
    }

    BYTE* baseAddress = (BYTE*)moduleInfo.lpBaseOfDll;
    SIZE_T moduleSize = moduleInfo.SizeOfImage;

    // Scan for INT3 instructions (0xCC)
    for (SIZE_T i = 0; i < moduleSize - 1; i++) {
        if (baseAddress[i] == 0xCC) {
            return true; // Software breakpoint found
        }
    }

    return false;
}

// Additional utility functions
std::string GetLastErrorString() {
    DWORD errorCode = GetLastError();
    LPSTR messageBuffer = nullptr;

    size_t size = FormatMessageA(
        FORMAT_MESSAGE_ALLOCATE_BUFFER | FORMAT_MESSAGE_FROM_SYSTEM | FORMAT_MESSAGE_IGNORE_INSERTS,
        NULL,
        errorCode,
        MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT),
        (LPSTR)&messageBuffer,
        0,
        NULL
    );

    std::string message(messageBuffer, size);
    LocalFree(messageBuffer);

    return message;
}

void SecureZeroMemoryEx(void* ptr, size_t size) {
    if (ptr != nullptr && size > 0) {
        SecureZeroMemory(ptr, size);
    }
}

bool IsRunningInVirtualMachine() {
    // Check for common VM artifacts

    // Check registry keys
    HKEY hKey;
    if (RegOpenKeyExA(HKEY_LOCAL_MACHINE, "SYSTEM\\CurrentControlSet\\Services\\VBoxService", 0, KEY_READ, &hKey) == ERROR_SUCCESS) {
        RegCloseKey(hKey);
        return true; // VirtualBox detected
    }

    if (RegOpenKeyExA(HKEY_LOCAL_MACHINE, "SOFTWARE\\VMware, Inc.\\VMware Tools", 0, KEY_READ, &hKey) == ERROR_SUCCESS) {
        RegCloseKey(hKey);
        return true; // VMware detected
    }

    // Check for VM-specific processes
    HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (hSnapshot != INVALID_HANDLE_VALUE) {
        PROCESSENTRY32 pe32;
        pe32.dwSize = sizeof(PROCESSENTRY32);

        if (Process32First(hSnapshot, &pe32)) {
            do {
                std::string processName = pe32.szExeFile;
                std::transform(processName.begin(), processName.end(), processName.begin(), ::tolower);

                if (processName.find("vboxservice") != std::string::npos ||
                    processName.find("vmtoolsd") != std::string::npos ||
                    processName.find("vmwaretray") != std::string::npos ||
                    processName.find("vmwareuser") != std::string::npos) {
                    CloseHandle(hSnapshot);
                    return true;
                }
            } while (Process32Next(hSnapshot, &pe32));
        }
        CloseHandle(hSnapshot);
    }

    return false;
}

// Additional MemoryProtection member function implementations
void MemoryProtection::AddWhitelistedProcess(const std::string& processName) {
    std::lock_guard<std::mutex> lock(g_protectionMutex);
    std::string lowerName = processName;
    std::transform(lowerName.begin(), lowerName.end(), lowerName.begin(), ::tolower);
    m_whitelistedProcesses.insert(lowerName);
}

void MemoryProtection::AddBlacklistedProcess(const std::string& processName) {
    std::lock_guard<std::mutex> lock(g_protectionMutex);
    std::string lowerName = processName;
    std::transform(lowerName.begin(), lowerName.end(), lowerName.begin(), ::tolower);
    m_blacklistedProcesses.insert(lowerName);
}

void MemoryProtection::RemoveWhitelistedProcess(const std::string& processName) {
    std::lock_guard<std::mutex> lock(g_protectionMutex);
    std::string lowerName = processName;
    std::transform(lowerName.begin(), lowerName.end(), lowerName.begin(), ::tolower);
    m_whitelistedProcesses.erase(lowerName);
}

bool MemoryProtection::IsProtectionActive() {
    return m_bProtectionActive.load();
}

std::vector<ProcessHandleInfo> MemoryProtection::GetSuspiciousHandles() {
    std::lock_guard<std::mutex> lock(g_protectionMutex);
    return m_suspiciousHandles;
}

void MemoryProtection::ClearSuspiciousHandles() {
    std::lock_guard<std::mutex> lock(g_protectionMutex);
    m_suspiciousHandles.clear();
}

void MemoryProtection::GenerateSecurityReport() {
    std::lock_guard<std::mutex> lock(g_protectionMutex);

    std::ostringstream report;
    report << "=== Project 404 Security Report ===\n";
    report << "Protection Active: " << (m_bProtectionActive.load() ? "Yes" : "No") << "\n";
    report << "Security Violations Detected: " << (g_bSecurityViolationDetected ? "Yes" : "No") << "\n";
    report << "Suspicious Handles Count: " << m_suspiciousHandles.size() << "\n";
    report << "Protected Memory Regions: " << m_protectedRegions.size() << "\n\n";

    if (!m_suspiciousHandles.empty()) {
        report << "Suspicious Processes:\n";
        for (const auto& handle : m_suspiciousHandles) {
            report << "  - PID: " << handle.processId
                   << ", Name: " << handle.processName
                   << ", Path: " << handle.processPath << "\n";
        }
    }

    OutputDebugStringA(report.str().c_str());
}

bool MemoryProtection::StartHandleMonitoring() {
    if (m_bProtectionActive.load()) {
        return true; // Already started
    }
    return Initialize();
}

void MemoryProtection::StopHandleMonitoring() {
    Shutdown();
}

bool MemoryProtection::DetectDebuggers() {
    return IsDebuggerPresent_Advanced() ||
           IsRemoteDebuggerPresent_Advanced() ||
           CheckForHardwareBreakpoints() ||
           CheckForSoftwareBreakpoints();
}

bool MemoryProtection::DetectVirtualMachines() {
    return IsRunningInVirtualMachine();
}

bool MemoryProtection::SetupGuardPages() {
    // This would set up guard pages around critical memory regions
    // For now, we'll just return true as this is a complex implementation
    // that would require specific knowledge of the game's memory layout
    return true;
}

// Additional detection functions
bool DetectWinAPIOverride() {
    // Check for WinAPIOverride process
    HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (hSnapshot != INVALID_HANDLE_VALUE) {
        PROCESSENTRY32 pe32;
        pe32.dwSize = sizeof(PROCESSENTRY32);

        if (Process32First(hSnapshot, &pe32)) {
            do {
                std::string processName = pe32.szExeFile;
                std::transform(processName.begin(), processName.end(), processName.begin(), ::tolower);

                if (processName.find("winapioverride") != std::string::npos) {
                    CloseHandle(hSnapshot);
                    return true;
                }
            } while (Process32Next(hSnapshot, &pe32));
        }
        CloseHandle(hSnapshot);
    }

    return false;
}

bool CheckSystemIntegrity() {
    // Basic system integrity checks
    // Check if critical system files have been modified

    // Check if our own module has been tampered with
    HMODULE hModule = GetModuleHandleA("Engine.dll");
    if (hModule == NULL) {
        return false;
    }

    // Additional integrity checks can be added here
    return true;
}

bool DetectHooks() {
    // Check for API hooks in critical functions
    // This is a simplified version - a full implementation would check
    // for inline hooks, IAT hooks, etc.

    HMODULE hKernel32 = GetModuleHandleA("kernel32.dll");
    if (hKernel32 == NULL) {
        return false;
    }

    // Check if OpenProcess has been hooked
    FARPROC pOpenProcess = GetProcAddress(hKernel32, "OpenProcess");
    if (pOpenProcess == NULL) {
        return false;
    }

    // Check first few bytes for common hook patterns
    BYTE* pBytes = (BYTE*)pOpenProcess;
    if (pBytes[0] == 0xE9 || pBytes[0] == 0xEB) { // JMP instruction
        return true; // Likely hooked
    }

    return false;
}

bool ValidateModuleIntegrity() {
    // Validate that our module hasn't been tampered with
    HMODULE hModule = GetModuleHandleA("Engine.dll");
    if (hModule == NULL) {
        return false;
    }

    MODULEINFO moduleInfo;
    if (!GetModuleInformation(GetCurrentProcess(), hModule, &moduleInfo, sizeof(moduleInfo))) {
        return false;
    }

    // Additional validation logic would go here
    // For now, just return true if we can get module info
    return true;
}
