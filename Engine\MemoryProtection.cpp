#include "MemoryProtection.h"
#include "Variables.h"
#include <iostream>
#include <sstream>
#include <algorithm>
#include <mutex>

// Include Detours for API hooking
#include "Detours/detours.h"

// Link required libraries
#pragma comment(lib, "psapi.lib")
#pragma comment(lib, "advapi32.lib")
#pragma comment(lib, "Detours/detours.lib")

// Global variables
volatile bool g_bMemoryProtectionEnabled = false;
volatile bool g_bSecurityViolationDetected = false;

// Static member initialization
std::atomic<bool> MemoryProtection::m_bProtectionActive(false);
std::atomic<bool> MemoryProtection::m_bShutdownRequested(false);
std::vector<ProcessHandleInfo> MemoryProtection::m_suspiciousHandles;
std::vector<ProtectedMemoryRegion> MemoryProtection::m_protectedRegions;
std::set<std::string> MemoryProtection::m_whitelistedProcesses;
std::set<std::string> MemoryProtection::m_blacklistedProcesses;
std::thread MemoryProtection::m_handleMonitorThread;
std::thread MemoryProtection::m_memoryIntegrityThread;

static std::mutex g_protectionMutex;

bool MemoryProtection::Initialize() {
    std::lock_guard<std::mutex> lock(g_protectionMutex);

    OutputDebugStringA("[DEBUG] MemoryProtection::Initialize() called\n");

    if (m_bProtectionActive.load()) {
        OutputDebugStringA("[DEBUG] Memory protection already initialized\n");
        return true; // Already initialized
    }
    
    // Initialize whitelisted processes (system processes that legitimately access memory)
    m_whitelistedProcesses.insert("system");
    m_whitelistedProcesses.insert("csrss.exe");
    m_whitelistedProcesses.insert("winlogon.exe");
    m_whitelistedProcesses.insert("services.exe");
    m_whitelistedProcesses.insert("lsass.exe");
    m_whitelistedProcesses.insert("svchost.exe");
    m_whitelistedProcesses.insert("explorer.exe");
    m_whitelistedProcesses.insert("dwm.exe");
    m_whitelistedProcesses.insert("conhost.exe");

    // Additional legitimate processes that may access game memory
    m_whitelistedProcesses.insert("taskmgr.exe");          // Task Manager
    m_whitelistedProcesses.insert("perfmon.exe");          // Performance Monitor
    m_whitelistedProcesses.insert("procexp.exe");          // Process Explorer (if user has it)
    m_whitelistedProcesses.insert("procexp64.exe");        // Process Explorer 64-bit
    m_whitelistedProcesses.insert("tasklist.exe");         // Task List command
    m_whitelistedProcesses.insert("werfault.exe");         // Windows Error Reporting
    m_whitelistedProcesses.insert("wermgr.exe");           // Windows Error Reporting Manager
    m_whitelistedProcesses.insert("audiodg.exe");          // Audio Device Graph Isolation
    m_whitelistedProcesses.insert("fontdrvhost.exe");      // Font Driver Host
    m_whitelistedProcesses.insert("sihost.exe");           // Shell Infrastructure Host
    m_whitelistedProcesses.insert("ctfmon.exe");           // CTF Loader
    m_whitelistedProcesses.insert("searchindexer.exe");    // Windows Search Indexer
    m_whitelistedProcesses.insert("searchprotocolhost.exe"); // Search Protocol Host
    m_whitelistedProcesses.insert("runtimebroker.exe");    // Runtime Broker
    m_whitelistedProcesses.insert("backgroundtaskhost.exe"); // Background Task Host
    m_whitelistedProcesses.insert("applicationframehost.exe"); // Application Frame Host
    m_whitelistedProcesses.insert("winstore.app.exe");     // Windows Store App
    m_whitelistedProcesses.insert("smartscreen.exe");      // Windows Defender SmartScreen
    m_whitelistedProcesses.insert("antimalware service executable"); // Windows Defender
    m_whitelistedProcesses.insert("msmpeng.exe");          // Windows Defender Antimalware Service
    m_whitelistedProcesses.insert("nissrv.exe");           // Windows Defender Network Inspection Service
    
    // Initialize blacklisted processes (known memory editing tools)
    m_blacklistedProcesses.insert("cheatengine-x86_64.exe");
    m_blacklistedProcesses.insert("cheatengine-i386.exe");
    m_blacklistedProcesses.insert("cheatengine.exe");
    m_blacklistedProcesses.insert("processhacker.exe");
    m_blacklistedProcesses.insert("x64dbg.exe");
    m_blacklistedProcesses.insert("x32dbg.exe");
    m_blacklistedProcesses.insert("ollydbg.exe");
    m_blacklistedProcesses.insert("ida.exe");
    m_blacklistedProcesses.insert("ida64.exe");
    m_blacklistedProcesses.insert("windbg.exe");
    m_blacklistedProcesses.insert("memorysharp.exe");
    m_blacklistedProcesses.insert("artmoney.exe");
    m_blacklistedProcesses.insert("gameguardian.exe");
    m_blacklistedProcesses.insert("speedhack.exe");
    m_blacklistedProcesses.insert("tsearch.exe");
    
    // Set up exception handler for memory access violations
    SetUnhandledExceptionFilter(MemoryProtection::MemoryAccessViolationHandler);

#if ENABLE_API_HOOKS
    // Set up API hooks to intercept memory access attempts
    if (!SetupMemoryAccessHooks()) {
        OutputDebugStringA("[WARNING] Failed to setup memory access hooks\n");
    } else {
        OutputDebugStringA("[DEBUG] API hooks enabled for enhanced memory protection\n");
    }
#else
    OutputDebugStringA("[DEBUG] API hooks disabled - using detection-only mode\n");
#endif
    
    // Start monitoring threads
    m_bShutdownRequested.store(false);
    m_handleMonitorThread = std::thread(HandleMonitorThreadFunc);
    m_memoryIntegrityThread = std::thread(MemoryIntegrityThreadFunc);
    
    m_bProtectionActive.store(true);
    g_bMemoryProtectionEnabled = true;

    OutputDebugStringA("[DEBUG] Memory protection system initialized successfully\n");
    OutputDebugStringA("[DEBUG] Configuration: DEBUGGER_DETECTION=0, HANDLE_MONITORING=0, MEMORY_INTEGRITY=0\n");

    return true;
}

void MemoryProtection::Shutdown() {
    std::lock_guard<std::mutex> lock(g_protectionMutex);
    
    if (!m_bProtectionActive.load()) {
        return; // Already shut down
    }
    
    m_bShutdownRequested.store(true);

#if ENABLE_API_HOOKS
    // Remove memory access hooks
    RemoveMemoryAccessHooks();
#endif

    // Wait for threads to finish
    if (m_handleMonitorThread.joinable()) {
        m_handleMonitorThread.join();
    }
    if (m_memoryIntegrityThread.joinable()) {
        m_memoryIntegrityThread.join();
    }
    
    // Unprotect all memory regions
    for (auto& region : m_protectedRegions) {
        if (region.isActive) {
            UnprotectMemoryRegion(region.baseAddress);
        }
    }
    m_protectedRegions.clear();
    
    m_bProtectionActive.store(false);
    g_bMemoryProtectionEnabled = false;
}

bool MemoryProtection::DetectSuspiciousHandles() {
    HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (hSnapshot == INVALID_HANDLE_VALUE) {
        return false;
    }

    PROCESSENTRY32 pe32;
    pe32.dwSize = sizeof(PROCESSENTRY32);

    bool suspiciousActivityDetected = false;
    DWORD currentProcessId = GetCurrentProcessId();

    if (Process32First(hSnapshot, &pe32)) {
        do {
            if (pe32.th32ProcessID == currentProcessId) {
                continue; // Skip our own process
            }

            // Check if process is blacklisted
            std::string processName = pe32.szExeFile;
            std::transform(processName.begin(), processName.end(), processName.begin(), ::tolower);

            if (IsProcessBlacklisted(processName)) {
                std::string blacklistMsg = "[SECURITY] BLACKLISTED PROCESS DETECTED: " + processName + "\n";
                OutputDebugStringA(blacklistMsg.c_str());

                ProcessHandleInfo handleInfo;
                handleInfo.processId = pe32.th32ProcessID;
                handleInfo.processName = processName;
                handleInfo.processPath = GetProcessPathFromPID(pe32.th32ProcessID);
                handleInfo.accessRights = SUSPICIOUS_ACCESS_RIGHTS;
                handleInfo.isWhitelisted = false;

                m_suspiciousHandles.push_back(handleInfo);
                LogSuspiciousActivity(handleInfo);
                suspiciousActivityDetected = true;

                // Immediate response for blacklisted processes
                TriggerSecurityResponse("Blacklisted memory editing tool detected: " + processName);
                continue;
            }

            // NEW: More intelligent suspicious pattern detection
            // Only flag processes that show multiple suspicious indicators
            if (IsSuspiciousProcessPattern(processName, pe32.th32ProcessID)) {
                std::string suspiciousMsg = "[SECURITY] SUSPICIOUS PATTERN DETECTED: " + processName + "\n";
                OutputDebugStringA(suspiciousMsg.c_str());

                ProcessHandleInfo handleInfo;
                handleInfo.processId = pe32.th32ProcessID;
                handleInfo.processName = processName;
                handleInfo.processPath = GetProcessPathFromPID(pe32.th32ProcessID);
                handleInfo.accessRights = SUSPICIOUS_ACCESS_RIGHTS;
                handleInfo.isWhitelisted = false;

                m_suspiciousHandles.push_back(handleInfo);
                LogSuspiciousActivity(handleInfo);
                suspiciousActivityDetected = true;

                // Less aggressive response - just log for now
                // TriggerSecurityResponse("Suspicious process pattern detected: " + processName);
            }

        } while (Process32Next(hSnapshot, &pe32));
    }

    CloseHandle(hSnapshot);
    return suspiciousActivityDetected;
}

bool MemoryProtection::DetectMemoryEditingTools() {
    bool detected = false;
    
    // Check for specific memory editing tools
    if (DetectCheatEngine()) {
        TriggerSecurityResponse("Cheat Engine detected");
        detected = true;
    }
    
    if (DetectProcessHacker()) {
        TriggerSecurityResponse("Process Hacker detected");
        detected = true;
    }
    
    if (DetectMemorySharp()) {
        TriggerSecurityResponse("MemorySharp detected");
        detected = true;
    }
    
    if (Detectx64dbg()) {
        TriggerSecurityResponse("x64dbg detected");
        detected = true;
    }
    
    if (DetectOllyDbg()) {
        TriggerSecurityResponse("OllyDbg detected");
        detected = true;
    }
    
    if (DetectIDA()) {
        TriggerSecurityResponse("IDA Pro detected");
        detected = true;
    }
    

    return detected;
}

void MemoryProtection::HandleMonitorThreadFunc() {
    while (!m_bShutdownRequested.load()) {
        try {
            // Add debug logging
            OutputDebugStringA("[DEBUG] Handle monitor thread running...\n");

#if ENABLE_HANDLE_MONITORING
            if (DetectSuspiciousHandles()) {
                OutputDebugStringA("[DEBUG] DetectSuspiciousHandles returned true\n");
                g_bSecurityViolationDetected = true;
            }

            if (DetectMemoryEditingTools()) {
                OutputDebugStringA("[DEBUG] DetectMemoryEditingTools returned true\n");
                g_bSecurityViolationDetected = true;
            }
#else
            OutputDebugStringA("[DEBUG] Handle monitoring is DISABLED\n");
#endif
            
            // Clean up old suspicious handle entries (older than 5 minutes)
            SYSTEMTIME currentTime;
            GetSystemTime(&currentTime);
            
            m_suspiciousHandles.erase(
                std::remove_if(m_suspiciousHandles.begin(), m_suspiciousHandles.end(),
                    [&currentTime](const ProcessHandleInfo& info) {
                        FILETIME currentFT, detectionFT;
                        SystemTimeToFileTime(&currentTime, &currentFT);
                        SystemTimeToFileTime(&info.detectionTime, &detectionFT);
                        
                        ULARGE_INTEGER current, detection;
                        current.LowPart = currentFT.dwLowDateTime;
                        current.HighPart = currentFT.dwHighDateTime;
                        detection.LowPart = detectionFT.dwLowDateTime;
                        detection.HighPart = detectionFT.dwHighDateTime;
                        
                        // Remove entries older than 5 minutes (300 seconds)
                        return (current.QuadPart - detection.QuadPart) > (300ULL * 10000000ULL);
                    }),
                m_suspiciousHandles.end()
            );
            
        } catch (...) {
            // Handle any exceptions to prevent thread termination
        }
        
        Sleep(HANDLE_CHECK_INTERVAL);
    }
}

void MemoryProtection::MemoryIntegrityThreadFunc() {
    while (!m_bShutdownRequested.load()) {
        try {
            OutputDebugStringA("[DEBUG] Memory integrity thread running...\n");

#if ENABLE_MEMORY_INTEGRITY
            if (!CheckMemoryIntegrity()) {
                OutputDebugStringA("[DEBUG] Memory integrity check failed\n");
                TriggerSecurityResponse("Memory integrity violation detected");
                g_bSecurityViolationDetected = true;
            }
#else
            OutputDebugStringA("[DEBUG] Memory integrity checking is DISABLED\n");
#endif

#if ENABLE_DEBUGGER_DETECTION
            if (DetectDebuggers()) {
                OutputDebugStringA("[DEBUG] Debugger detected\n");
                TriggerSecurityResponse("Debugger detected");
                g_bSecurityViolationDetected = true;
            }
#endif

        } catch (...) {
            // Handle any exceptions to prevent thread termination
            OutputDebugStringA("[DEBUG] Exception in memory integrity thread\n");
        }

        Sleep(MEMORY_CHECK_INTERVAL);
    }
}

// Utility function implementations
bool MemoryProtection::IsProcessWhitelisted(const std::string& processName) {
    std::string lowerName = processName;
    std::transform(lowerName.begin(), lowerName.end(), lowerName.begin(), ::tolower);
    return m_whitelistedProcesses.find(lowerName) != m_whitelistedProcesses.end();
}

bool MemoryProtection::IsProcessBlacklisted(const std::string& processName) {
    std::string lowerName = processName;
    std::transform(lowerName.begin(), lowerName.end(), lowerName.begin(), ::tolower);
    return m_blacklistedProcesses.find(lowerName) != m_blacklistedProcesses.end();
}

std::string MemoryProtection::GetProcessNameFromPID(DWORD processId) {
    HANDLE hProcess = OpenProcess(PROCESS_QUERY_INFORMATION | PROCESS_VM_READ, FALSE, processId);
    if (hProcess == NULL) {
        return "Unknown";
    }

    char processName[MAX_PATH];
    if (GetModuleBaseNameA(hProcess, NULL, processName, sizeof(processName))) {
        CloseHandle(hProcess);
        return std::string(processName);
    }

    CloseHandle(hProcess);
    return "Unknown";
}

std::string MemoryProtection::GetProcessPathFromPID(DWORD processId) {
    HANDLE hProcess = OpenProcess(PROCESS_QUERY_INFORMATION | PROCESS_VM_READ, FALSE, processId);
    if (hProcess == NULL) {
        return "Unknown";
    }

    char processPath[MAX_PATH];
    if (GetModuleFileNameExA(hProcess, NULL, processPath, sizeof(processPath))) {
        CloseHandle(hProcess);
        return std::string(processPath);
    }

    CloseHandle(hProcess);
    return "Unknown";
}

void MemoryProtection::LogSuspiciousActivity(const ProcessHandleInfo& handleInfo) {
    // Log to debug output or file
    std::ostringstream logMessage;
    logMessage << "[SECURITY] Suspicious process detected: "
               << "PID=" << handleInfo.processId
               << ", Name=" << handleInfo.processName
               << ", Path=" << handleInfo.processPath
               << ", AccessRights=0x" << std::hex << handleInfo.accessRights;

    OutputDebugStringA(logMessage.str().c_str());
}

void MemoryProtection::TriggerSecurityResponse(const std::string& reason) {
    // Set global flag
    g_bSecurityViolationDetected = true;

    // Log the security violation with more detail
    std::string logMessage = "[SECURITY VIOLATION] " + reason;
    OutputDebugStringA(logMessage.c_str());

    // TEMPORARY: Show detailed message for debugging
    std::string message = "Security violation detected: " + reason +
                         "\n\nThis is likely a false positive. Check debug output for details." +
                         "\n\nThe application will now terminate to protect Project 404 integrity.";
    MessageBoxA(NULL, message.c_str(), "Project 404 - Security Alert (DEBUG)", MB_OK | MB_ICONERROR);

    // Set the global stop flag and exit
    extern bool StopClient;
    StopClient = true;
    exit(1);
}

bool MemoryProtection::CheckMemoryIntegrity() {
    // Check if any protected memory regions have been modified
    for (const auto& region : m_protectedRegions) {
        if (!region.isActive) continue;

        MEMORY_BASIC_INFORMATION mbi;
        if (VirtualQuery(region.baseAddress, &mbi, sizeof(mbi)) == 0) {
            return false; // Failed to query memory
        }

        // Check if protection has been changed
        if (mbi.Protect != region.currentProtection) {
            return false; // Memory protection has been modified
        }
    }

    return true;
}

bool MemoryProtection::ProtectMemoryRegion(LPVOID baseAddress, SIZE_T size, const std::string& regionName) {
    DWORD oldProtection;
    if (!VirtualProtect(baseAddress, size, PAGE_READONLY, &oldProtection)) {
        return false;
    }

    ProtectedMemoryRegion region;
    region.baseAddress = baseAddress;
    region.size = size;
    region.originalProtection = oldProtection;
    region.currentProtection = PAGE_READONLY;
    region.regionName = regionName;
    region.isActive = true;

    m_protectedRegions.push_back(region);
    return true;
}

bool MemoryProtection::UnprotectMemoryRegion(LPVOID baseAddress) {
    for (auto& region : m_protectedRegions) {
        if (region.baseAddress == baseAddress && region.isActive) {
            DWORD oldProtection;
            if (VirtualProtect(baseAddress, region.size, region.originalProtection, &oldProtection)) {
                region.isActive = false;
                return true;
            }
            return false;
        }
    }
    return false;
}

// Exception handler for memory access violations
LONG WINAPI MemoryProtection::MemoryAccessViolationHandler(EXCEPTION_POINTERS* exceptionInfo) {
    if (exceptionInfo->ExceptionRecord->ExceptionCode == EXCEPTION_ACCESS_VIOLATION) {
        // Log the access violation
        ULONG_PTR faultAddress = exceptionInfo->ExceptionRecord->ExceptionInformation[1];
        std::ostringstream logMessage;
        logMessage << "[SECURITY] Memory access violation at address: 0x"
                   << std::hex << faultAddress;
        OutputDebugStringA(logMessage.str().c_str());

        // Check if this is an attempt to access protected memory
        for (const auto& region : m_protectedRegions) {
            if (region.isActive &&
                faultAddress >= (ULONG_PTR)region.baseAddress &&
                faultAddress < (ULONG_PTR)region.baseAddress + region.size) {

                TriggerSecurityResponse("Unauthorized memory access detected");
                return EXCEPTION_EXECUTE_HANDLER;
            }
        }
    }

    return EXCEPTION_CONTINUE_SEARCH;
}

// Memory editing tool detection functions
bool DetectCheatEngine() {
    // Check for Cheat Engine windows
    HWND hWnd = FindWindowA("TMainForm", NULL);
    if (hWnd != NULL) {
        char windowTitle[256];
        GetWindowTextA(hWnd, windowTitle, sizeof(windowTitle));
        std::string title = windowTitle;
        std::transform(title.begin(), title.end(), title.begin(), ::tolower);
        if (title.find("cheat engine") != std::string::npos) {
            return true;
        }
    }

    // Check for Cheat Engine processes
    HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (hSnapshot != INVALID_HANDLE_VALUE) {
        PROCESSENTRY32 pe32;
        pe32.dwSize = sizeof(PROCESSENTRY32);

        if (Process32First(hSnapshot, &pe32)) {
            do {
                std::string processName = pe32.szExeFile;
                std::transform(processName.begin(), processName.end(), processName.begin(), ::tolower);

                if (processName.find("cheatengine") != std::string::npos ||
                    processName.find("cheat engine") != std::string::npos ||
                    processName == "cheatengine.exe" ||
                    processName == "cheatengine-x86_64.exe" ||
                    processName == "cheatengine-i386.exe") {
                    CloseHandle(hSnapshot);
                    return true;
                }
            } while (Process32Next(hSnapshot, &pe32));
        }
        CloseHandle(hSnapshot);
    }

    return false;
}

bool DetectProcessHacker() {
    // Check for Process Hacker window
    HWND hWnd = FindWindowA("ProcessHacker", NULL);
    if (hWnd != NULL) return true;

    // Check for Process Hacker process
    HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (hSnapshot != INVALID_HANDLE_VALUE) {
        PROCESSENTRY32 pe32;
        pe32.dwSize = sizeof(PROCESSENTRY32);

        if (Process32First(hSnapshot, &pe32)) {
            do {
                std::string processName = pe32.szExeFile;
                std::transform(processName.begin(), processName.end(), processName.begin(), ::tolower);

                if (processName.find("processhacker") != std::string::npos ||
                    processName == "processhacker.exe") {
                    CloseHandle(hSnapshot);
                    return true;
                }
            } while (Process32Next(hSnapshot, &pe32));
        }
        CloseHandle(hSnapshot);
    }

    return false;
}

bool DetectMemorySharp() {
    // Enhanced MemorySharp detection - check for .NET processes with suspicious behavior
    HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (hSnapshot != INVALID_HANDLE_VALUE) {
        PROCESSENTRY32 pe32;
        pe32.dwSize = sizeof(PROCESSENTRY32);
        DWORD currentProcessId = GetCurrentProcessId();

        if (Process32First(hSnapshot, &pe32)) {
            do {
                if (pe32.th32ProcessID == currentProcessId) {
                    continue; // Skip our own process
                }

                std::string processName = pe32.szExeFile;
                std::transform(processName.begin(), processName.end(), processName.begin(), ::tolower);

                // Check for common MemorySharp-based tools
                if (processName.find("memorysharp") != std::string::npos ||
                    processName.find("memoryhacker") != std::string::npos ||
                    processName.find("memoryeditor") != std::string::npos ||
                    processName.find("memorywriter") != std::string::npos ||
                    processName.find("processeditor") != std::string::npos) {
                    CloseHandle(hSnapshot);
                    return true;
                }


            } while (Process32Next(hSnapshot, &pe32));
        }
        CloseHandle(hSnapshot);
    }

    return false;
}



bool Detectx64dbg() {
    // Check for x64dbg/x32dbg windows
    HWND hWnd = FindWindowA("Qt5QWindowIcon", NULL);
    if (hWnd != NULL) {
        char windowTitle[256];
        GetWindowTextA(hWnd, windowTitle, sizeof(windowTitle));
        std::string title = windowTitle;
        std::transform(title.begin(), title.end(), title.begin(), ::tolower);
        if (title.find("x64dbg") != std::string::npos || title.find("x32dbg") != std::string::npos) {
            return true;
        }
    }

    // Check for x64dbg processes
    HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (hSnapshot != INVALID_HANDLE_VALUE) {
        PROCESSENTRY32 pe32;
        pe32.dwSize = sizeof(PROCESSENTRY32);

        if (Process32First(hSnapshot, &pe32)) {
            do {
                std::string processName = pe32.szExeFile;
                std::transform(processName.begin(), processName.end(), processName.begin(), ::tolower);

                if (processName == "x64dbg.exe" || processName == "x32dbg.exe") {
                    CloseHandle(hSnapshot);
                    return true;
                }
            } while (Process32Next(hSnapshot, &pe32));
        }
        CloseHandle(hSnapshot);
    }

    return false;
}

bool DetectOllyDbg() {
    // Check for OllyDbg window
    HWND hWnd = FindWindowA("OllyDbg", NULL);
    if (hWnd != NULL) return true;

    // Check for OllyDbg process
    HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (hSnapshot != INVALID_HANDLE_VALUE) {
        PROCESSENTRY32 pe32;
        pe32.dwSize = sizeof(PROCESSENTRY32);

        if (Process32First(hSnapshot, &pe32)) {
            do {
                std::string processName = pe32.szExeFile;
                std::transform(processName.begin(), processName.end(), processName.begin(), ::tolower);

                if (processName.find("ollydbg") != std::string::npos) {
                    CloseHandle(hSnapshot);
                    return true;
                }
            } while (Process32Next(hSnapshot, &pe32));
        }
        CloseHandle(hSnapshot);
    }

    return false;
}

bool DetectIDA() {
    // Check for IDA Pro processes
    HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (hSnapshot != INVALID_HANDLE_VALUE) {
        PROCESSENTRY32 pe32;
        pe32.dwSize = sizeof(PROCESSENTRY32);

        if (Process32First(hSnapshot, &pe32)) {
            do {
                std::string processName = pe32.szExeFile;
                std::transform(processName.begin(), processName.end(), processName.begin(), ::tolower);

                if (processName == "ida.exe" || processName == "ida64.exe" ||
                    processName == "idaw.exe" || processName == "idaw64.exe") {
                    CloseHandle(hSnapshot);
                    return true;
                }
            } while (Process32Next(hSnapshot, &pe32));
        }
        CloseHandle(hSnapshot);
    }

    return false;
}

// Advanced anti-debugging functions (made less aggressive to avoid false positives)
bool IsDebuggerPresent_Advanced() {
    // Only use standard Windows API check for now to avoid false positives
    // The manual PEB checks can trigger false positives in some environments
    return IsDebuggerPresent();

    /* DISABLED - Too aggressive, causing false positives
    // Check PEB (Process Environment Block) manually
    __try {
        __asm {
            mov eax, fs:[30h]    // Get PEB address
            mov al, [eax + 2h]   // Get BeingDebugged flag
            test al, al
            jnz debugger_detected
        }

        // Check NtGlobalFlag in PEB
        __asm {
            mov eax, fs:[30h]    // Get PEB address
            mov eax, [eax + 68h] // Get NtGlobalFlag
            and eax, 70h         // Check for heap flags
            jnz debugger_detected
        }

        // Check heap flags
        __asm {
            mov eax, fs:[30h]    // Get PEB address
            mov eax, [eax + 18h] // Get ProcessHeap
            mov eax, [eax + 0Ch] // Get Flags
            and eax, 2           // Check HEAP_TAIL_CHECKING_ENABLED
            jnz debugger_detected
        }

        return false;

    debugger_detected:
        return true;
    }
    __except(EXCEPTION_EXECUTE_HANDLER) {
        return false; // Don't treat exceptions as debugger presence
    }
    */
}

bool IsRemoteDebuggerPresent_Advanced() {
    BOOL isRemoteDebuggerPresent = FALSE;

    // Use only the standard Windows API check to avoid false positives
    if (CheckRemoteDebuggerPresent(GetCurrentProcess(), &isRemoteDebuggerPresent)) {
        if (isRemoteDebuggerPresent) {
            return true;
        }
    }

    // DISABLED - Manual NtQueryInformationProcess check can cause false positives
    return false;

    /* DISABLED - Too aggressive
    // Manual check using NtQueryInformationProcess
    typedef NTSTATUS (WINAPI *pNtQueryInformationProcess)(
        HANDLE ProcessHandle,
        DWORD ProcessInformationClass,
        PVOID ProcessInformation,
        ULONG ProcessInformationLength,
        PULONG ReturnLength
    );

    HMODULE hNtdll = GetModuleHandleA("ntdll.dll");
    if (hNtdll) {
        pNtQueryInformationProcess NtQueryInformationProcess =
            (pNtQueryInformationProcess)GetProcAddress(hNtdll, "NtQueryInformationProcess");

        if (NtQueryInformationProcess) {
            DWORD debugPort = 0;
            NTSTATUS status = NtQueryInformationProcess(
                GetCurrentProcess(),
                7, // ProcessDebugPort
                &debugPort,
                sizeof(debugPort),
                NULL
            );

            if (status == 0 && debugPort != 0) {
                return true;
            }
        }
    }

    return false;
    */
}

bool CheckForHardwareBreakpoints() {
    // DISABLED - Can cause false positives in some environments
    // Hardware breakpoint detection can trigger on legitimate system operations
    return false;

    /* DISABLED - Too aggressive
    CONTEXT ctx;
    ctx.ContextFlags = CONTEXT_DEBUG_REGISTERS;

    if (GetThreadContext(GetCurrentThread(), &ctx)) {
        // Check if any debug registers are set
        if (ctx.Dr0 != 0 || ctx.Dr1 != 0 || ctx.Dr2 != 0 || ctx.Dr3 != 0) {
            return true;
        }

        // Check Dr7 for breakpoint enables
        if ((ctx.Dr7 & 0xFF) != 0) {
            return true;
        }
    }

    return false;
    */
}

bool CheckForSoftwareBreakpoints() {
    // DISABLED - Can cause false positives and performance issues
    // Software breakpoint scanning can be too aggressive for normal operation
    return false;

    /* DISABLED - Too aggressive and slow
    // Check for INT3 (0xCC) breakpoints in our code
    HMODULE hModule = GetModuleHandleA("Engine.dll");
    if (hModule == NULL) {
        return false;
    }

    MODULEINFO moduleInfo;
    if (!GetModuleInformation(GetCurrentProcess(), hModule, &moduleInfo, sizeof(moduleInfo))) {
        return false;
    }

    BYTE* baseAddress = (BYTE*)moduleInfo.lpBaseOfDll;
    SIZE_T moduleSize = moduleInfo.SizeOfImage;

    // Scan for INT3 instructions (0xCC)
    for (SIZE_T i = 0; i < moduleSize - 1; i++) {
        if (baseAddress[i] == 0xCC) {
            return true; // Software breakpoint found
        }
    }

    return false;
    */
}

// Additional utility functions
std::string GetLastErrorString() {
    DWORD errorCode = GetLastError();
    LPSTR messageBuffer = nullptr;

    size_t size = FormatMessageA(
        FORMAT_MESSAGE_ALLOCATE_BUFFER | FORMAT_MESSAGE_FROM_SYSTEM | FORMAT_MESSAGE_IGNORE_INSERTS,
        NULL,
        errorCode,
        MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT),
        (LPSTR)&messageBuffer,
        0,
        NULL
    );

    std::string message(messageBuffer, size);
    LocalFree(messageBuffer);

    return message;
}

void SecureZeroMemoryEx(void* ptr, size_t size) {
    if (ptr != nullptr && size > 0) {
        SecureZeroMemory(ptr, size);
    }
}

bool IsRunningInVirtualMachine() {
    // Check for common VM artifacts

    // Check registry keys
    HKEY hKey;
    if (RegOpenKeyExA(HKEY_LOCAL_MACHINE, "SYSTEM\\CurrentControlSet\\Services\\VBoxService", 0, KEY_READ, &hKey) == ERROR_SUCCESS) {
        RegCloseKey(hKey);
        return true; // VirtualBox detected
    }

    if (RegOpenKeyExA(HKEY_LOCAL_MACHINE, "SOFTWARE\\VMware, Inc.\\VMware Tools", 0, KEY_READ, &hKey) == ERROR_SUCCESS) {
        RegCloseKey(hKey);
        return true; // VMware detected
    }

    // Check for VM-specific processes
    HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (hSnapshot != INVALID_HANDLE_VALUE) {
        PROCESSENTRY32 pe32;
        pe32.dwSize = sizeof(PROCESSENTRY32);

        if (Process32First(hSnapshot, &pe32)) {
            do {
                std::string processName = pe32.szExeFile;
                std::transform(processName.begin(), processName.end(), processName.begin(), ::tolower);

                if (processName.find("vboxservice") != std::string::npos ||
                    processName.find("vmtoolsd") != std::string::npos ||
                    processName.find("vmwaretray") != std::string::npos ||
                    processName.find("vmwareuser") != std::string::npos) {
                    CloseHandle(hSnapshot);
                    return true;
                }
            } while (Process32Next(hSnapshot, &pe32));
        }
        CloseHandle(hSnapshot);
    }

    return false;
}

// Intelligent suspicious process pattern detection
bool IsSuspiciousProcessPattern(const std::string& processName, DWORD processId) {
    int suspiciousScore = 0;

    // Check for suspicious keywords in process name
    std::vector<std::string> suspiciousKeywords = {
        "hack", "cheat", "trainer", "mod", "inject", "debug", "memory",
        "edit", "patch", "crack", "bypass", "exploit", "bot"
    };

    for (const auto& keyword : suspiciousKeywords) {
        if (processName.find(keyword) != std::string::npos) {
            suspiciousScore += 2;
        }
    }

    // Check for suspicious file extensions or patterns
    if (processName.find(".tmp.exe") != std::string::npos ||
        processName.find("temp") != std::string::npos ||
        processName.find("unknown") != std::string::npos) {
        suspiciousScore += 1;
    }

    // Check if process has suspicious characteristics
    HANDLE hProcess = OpenProcess(PROCESS_QUERY_INFORMATION, FALSE, processId);
    if (hProcess != NULL) {
        // Check if process is running from temp directory
        char processPath[MAX_PATH];
        if (GetModuleFileNameExA(hProcess, NULL, processPath, sizeof(processPath))) {
            std::string pathStr = processPath;
            std::transform(pathStr.begin(), pathStr.end(), pathStr.begin(), ::tolower);

            if (pathStr.find("\\temp\\") != std::string::npos ||
                pathStr.find("\\tmp\\") != std::string::npos ||
                pathStr.find("\\appdata\\local\\temp\\") != std::string::npos) {
                suspiciousScore += 2;
            }

            // Check for unsigned executables in suspicious locations
            if (pathStr.find("\\users\\") != std::string::npos &&
                pathStr.find("\\downloads\\") != std::string::npos) {
                suspiciousScore += 1;
            }
        }
        CloseHandle(hProcess);
    }

    // Only flag as suspicious if score is high enough
    // This prevents false positives from legitimate software
    return suspiciousScore >= 3;
}

// Additional MemoryProtection member function implementations
void MemoryProtection::AddWhitelistedProcess(const std::string& processName) {
    std::lock_guard<std::mutex> lock(g_protectionMutex);
    std::string lowerName = processName;
    std::transform(lowerName.begin(), lowerName.end(), lowerName.begin(), ::tolower);
    m_whitelistedProcesses.insert(lowerName);
}

void MemoryProtection::AddBlacklistedProcess(const std::string& processName) {
    std::lock_guard<std::mutex> lock(g_protectionMutex);
    std::string lowerName = processName;
    std::transform(lowerName.begin(), lowerName.end(), lowerName.begin(), ::tolower);
    m_blacklistedProcesses.insert(lowerName);
}

void MemoryProtection::RemoveWhitelistedProcess(const std::string& processName) {
    std::lock_guard<std::mutex> lock(g_protectionMutex);
    std::string lowerName = processName;
    std::transform(lowerName.begin(), lowerName.end(), lowerName.begin(), ::tolower);
    m_whitelistedProcesses.erase(lowerName);
}

bool MemoryProtection::IsProtectionActive() {
    return m_bProtectionActive.load();
}

std::vector<ProcessHandleInfo> MemoryProtection::GetSuspiciousHandles() {
    std::lock_guard<std::mutex> lock(g_protectionMutex);
    return m_suspiciousHandles;
}

void MemoryProtection::ClearSuspiciousHandles() {
    std::lock_guard<std::mutex> lock(g_protectionMutex);
    m_suspiciousHandles.clear();
}

void MemoryProtection::GenerateSecurityReport() {
    std::lock_guard<std::mutex> lock(g_protectionMutex);

    std::ostringstream report;
    report << "=== Project 404 Security Report ===\n";
    report << "Protection Active: " << (m_bProtectionActive.load() ? "Yes" : "No") << "\n";
    report << "Security Violations Detected: " << (g_bSecurityViolationDetected ? "Yes" : "No") << "\n";
    report << "Suspicious Handles Count: " << m_suspiciousHandles.size() << "\n";
    report << "Protected Memory Regions: " << m_protectedRegions.size() << "\n\n";

    if (!m_suspiciousHandles.empty()) {
        report << "Suspicious Processes:\n";
        for (const auto& handle : m_suspiciousHandles) {
            report << "  - PID: " << handle.processId
                   << ", Name: " << handle.processName
                   << ", Path: " << handle.processPath << "\n";
        }
    }

    OutputDebugStringA(report.str().c_str());
}

bool MemoryProtection::StartHandleMonitoring() {
    if (m_bProtectionActive.load()) {
        return true; // Already started
    }
    return Initialize();
}

void MemoryProtection::StopHandleMonitoring() {
    Shutdown();
}

bool MemoryProtection::DetectDebuggers() {
#if ENABLE_DEBUGGER_DETECTION
    // Only use the most reliable detection methods to avoid false positives
    // Advanced methods are disabled to prevent legitimate users from being blocked
    return IsDebuggerPresent_Advanced() || IsRemoteDebuggerPresent_Advanced();
#else
    // Debugger detection is disabled to prevent false positives
    return false;
#endif

    // Disabled aggressive checks that can cause false positives:
    // CheckForHardwareBreakpoints() || CheckForSoftwareBreakpoints()
}

bool MemoryProtection::DetectVirtualMachines() {
    return IsRunningInVirtualMachine();
}

bool MemoryProtection::SetupGuardPages() {
    // This would set up guard pages around critical memory regions
    // For now, we'll just return true as this is a complex implementation
    // that would require specific knowledge of the game's memory layout
    return true;
}

// Additional detection functions
bool DetectWinAPIOverride() {
    // Check for WinAPIOverride process
    HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (hSnapshot != INVALID_HANDLE_VALUE) {
        PROCESSENTRY32 pe32;
        pe32.dwSize = sizeof(PROCESSENTRY32);

        if (Process32First(hSnapshot, &pe32)) {
            do {
                std::string processName = pe32.szExeFile;
                std::transform(processName.begin(), processName.end(), processName.begin(), ::tolower);

                if (processName.find("winapioverride") != std::string::npos) {
                    CloseHandle(hSnapshot);
                    return true;
                }
            } while (Process32Next(hSnapshot, &pe32));
        }
        CloseHandle(hSnapshot);
    }

    return false;
}

bool DetectHooks() {
    // Check for API hooks in critical functions
    // This is a simplified version - a full implementation would check
    // for inline hooks, IAT hooks, etc.

    HMODULE hKernel32 = GetModuleHandleA("kernel32.dll");
    if (hKernel32 == NULL) {
        return false;
    }

    // Check if OpenProcess has been hooked
    FARPROC pOpenProcess = GetProcAddress(hKernel32, "OpenProcess");
    if (pOpenProcess == NULL) {
        return false;
    }

    // Check first few bytes for common hook patterns
    BYTE* pBytes = (BYTE*)pOpenProcess;
    if (pBytes[0] == 0xE9 || pBytes[0] == 0xEB) { // JMP instruction
        return true; // Likely hooked
    }

    return false;
}

bool ValidateModuleIntegrity() {
    // Validate that our module hasn't been tampered with
    HMODULE hModule = GetModuleHandleA("Engine.dll");
    if (hModule == NULL) {
        return false;
    }

    MODULEINFO moduleInfo;
    if (!GetModuleInformation(GetCurrentProcess(), hModule, &moduleInfo, sizeof(moduleInfo))) {
        return false;
    }

    // Additional validation logic would go here
    // For now, just return true if we can get module info
    return true;
}

// API Hook implementations to block memory access
typedef HANDLE (WINAPI *pOpenProcess)(DWORD dwDesiredAccess, BOOL bInheritHandle, DWORD dwProcessId);
typedef BOOL (WINAPI *pReadProcessMemory)(HANDLE hProcess, LPCVOID lpBaseAddress, LPVOID lpBuffer, SIZE_T nSize, SIZE_T *lpNumberOfBytesRead);
typedef BOOL (WINAPI *pWriteProcessMemory)(HANDLE hProcess, LPVOID lpBaseAddress, LPCVOID lpBuffer, SIZE_T nSize, SIZE_T *lpNumberOfBytesWritten);
typedef BOOL (WINAPI *pVirtualProtectEx)(HANDLE hProcess, LPVOID lpAddress, SIZE_T dwSize, DWORD flNewProtect, PDWORD lpflOldProtect);

pOpenProcess OriginalOpenProcess = nullptr;
pReadProcessMemory OriginalReadProcessMemory = nullptr;
pWriteProcessMemory OriginalWriteProcessMemory = nullptr;
pVirtualProtectEx OriginalVirtualProtectEx = nullptr;

// Hooked OpenProcess function
HANDLE WINAPI HookedOpenProcess(DWORD dwDesiredAccess, BOOL bInheritHandle, DWORD dwProcessId) {
    DWORD currentProcessId = GetCurrentProcessId();

    // If someone is trying to open our process with suspicious access rights
    if (dwProcessId == currentProcessId) {
        // Only block if it's a combination of dangerous access rights
        DWORD dangerousAccess = PROCESS_VM_READ | PROCESS_VM_WRITE | PROCESS_VM_OPERATION;
        if ((dwDesiredAccess & dangerousAccess) == dangerousAccess) {
            // Get caller process info
            char callerPath[MAX_PATH];
            if (GetModuleFileNameA(NULL, callerPath, sizeof(callerPath))) {
                std::string callerName = callerPath;
                size_t lastSlash = callerName.find_last_of("\\/");
                if (lastSlash != std::string::npos) {
                    callerName = callerName.substr(lastSlash + 1);
                }
                std::transform(callerName.begin(), callerName.end(), callerName.begin(), ::tolower);

                // Allow whitelisted processes
                if (MemoryProtection::IsProcessWhitelisted(callerName)) {
                    return OriginalOpenProcess(dwDesiredAccess, bInheritHandle, dwProcessId);
                }

                // Only block if it's not our own process or a system process
                if (callerName != "engine.exe" && callerName != "engine.dll") {
                    // Log the attempt
                    std::string logMsg = "[SECURITY] Blocked suspicious OpenProcess from: " + callerName +
                                       " with access rights: 0x" + std::to_string(dwDesiredAccess) + "\n";
                    OutputDebugStringA(logMsg.c_str());

                    SetLastError(ERROR_ACCESS_DENIED);
                    return NULL;
                }
            }
        }
    }

    // Call original function
    return OriginalOpenProcess(dwDesiredAccess, bInheritHandle, dwProcessId);
}

// Hooked ReadProcessMemory function
BOOL WINAPI HookedReadProcessMemory(HANDLE hProcess, LPCVOID lpBaseAddress, LPVOID lpBuffer, SIZE_T nSize, SIZE_T *lpNumberOfBytesRead) {
#if ENABLE_READ_PROTECTION
    // Check if someone is trying to read our process memory
    DWORD processId = GetProcessId(hProcess);
    if (processId == GetCurrentProcessId()) {
        // Get caller process info
        char callerPath[MAX_PATH];
        if (GetModuleFileNameA(NULL, callerPath, sizeof(callerPath))) {
            std::string callerName = callerPath;
            size_t lastSlash = callerName.find_last_of("\\/");
            if (lastSlash != std::string::npos) {
                callerName = callerName.substr(lastSlash + 1);
            }
            std::transform(callerName.begin(), callerName.end(), callerName.begin(), ::tolower);

            // Allow whitelisted processes and our own process
            if (MemoryProtection::IsProcessWhitelisted(callerName) ||
                callerName == "engine.exe" || callerName == "engine.dll") {
                return OriginalReadProcessMemory(hProcess, lpBaseAddress, lpBuffer, nSize, lpNumberOfBytesRead);
            }

            // Only block if it's a large read operation (likely memory scanning)
            if (nSize > 1024) { // Only block reads larger than 1KB
                OutputDebugStringA("[SECURITY] Blocked large ReadProcessMemory attempt\n");

                // Just log for now, don't terminate immediately
                std::string logMsg = "[SECURITY] Suspicious memory read from: " + callerName +
                                   " size: " + std::to_string(nSize) + "\n";
                OutputDebugStringA(logMsg.c_str());

                SetLastError(ERROR_ACCESS_DENIED);
                return FALSE;
            }
        }
    }
#endif

    return OriginalReadProcessMemory(hProcess, lpBaseAddress, lpBuffer, nSize, lpNumberOfBytesRead);
}

// Hooked WriteProcessMemory function
BOOL WINAPI HookedWriteProcessMemory(HANDLE hProcess, LPVOID lpBaseAddress, LPCVOID lpBuffer, SIZE_T nSize, SIZE_T *lpNumberOfBytesWritten) {
#if ENABLE_WRITE_PROTECTION
    // Check if someone is trying to write to our process memory
    DWORD processId = GetProcessId(hProcess);
    if (processId == GetCurrentProcessId()) {
        // Get caller process info
        char callerPath[MAX_PATH];
        if (GetModuleFileNameA(NULL, callerPath, sizeof(callerPath))) {
            std::string callerName = callerPath;
            size_t lastSlash = callerName.find_last_of("\\/");
            if (lastSlash != std::string::npos) {
                callerName = callerName.substr(lastSlash + 1);
            }
            std::transform(callerName.begin(), callerName.end(), callerName.begin(), ::tolower);

            // Allow whitelisted processes and our own process
            if (MemoryProtection::IsProcessWhitelisted(callerName) ||
                callerName == "engine.exe" || callerName == "engine.dll") {
                return OriginalWriteProcessMemory(hProcess, lpBaseAddress, lpBuffer, nSize, lpNumberOfBytesWritten);
            }

            // Block any external write attempts (this is more critical than reads)
            OutputDebugStringA("[SECURITY] Blocked WriteProcessMemory attempt\n");

            std::string logMsg = "[SECURITY] Memory write attempt from: " + callerName +
                               " size: " + std::to_string(nSize) +
                               " address: 0x" + std::to_string((uintptr_t)lpBaseAddress) + "\n";
            OutputDebugStringA(logMsg.c_str());

            // Trigger security response for write attempts
            MemoryProtection::TriggerSecurityResponse("External memory write attempt detected from: " + callerName);
            SetLastError(ERROR_ACCESS_DENIED);
            return FALSE;
        }
    }
#endif

    return OriginalWriteProcessMemory(hProcess, lpBaseAddress, lpBuffer, nSize, lpNumberOfBytesWritten);
}

// Hooked VirtualProtectEx function
BOOL WINAPI HookedVirtualProtectEx(HANDLE hProcess, LPVOID lpAddress, SIZE_T dwSize, DWORD flNewProtect, PDWORD lpflOldProtect) {
    // Check if someone is trying to change memory protection of our process
    DWORD processId = GetProcessId(hProcess);
    if (processId == GetCurrentProcessId()) {
        // Get caller process info
        char callerPath[MAX_PATH];
        if (GetModuleFileNameA(NULL, callerPath, sizeof(callerPath))) {
            std::string callerName = callerPath;
            size_t lastSlash = callerName.find_last_of("\\/");
            if (lastSlash != std::string::npos) {
                callerName = callerName.substr(lastSlash + 1);
            }
            std::transform(callerName.begin(), callerName.end(), callerName.begin(), ::tolower);

            // Allow whitelisted processes and our own process
            if (MemoryProtection::IsProcessWhitelisted(callerName) ||
                callerName == "engine.exe" || callerName == "engine.dll") {
                return OriginalVirtualProtectEx(hProcess, lpAddress, dwSize, flNewProtect, lpflOldProtect);
            }

            // Only block if trying to make memory executable (common in code injection)
            if (flNewProtect & (PAGE_EXECUTE | PAGE_EXECUTE_READ | PAGE_EXECUTE_READWRITE | PAGE_EXECUTE_WRITECOPY)) {
                OutputDebugStringA("[SECURITY] Blocked VirtualProtectEx attempt to make memory executable\n");

                std::string logMsg = "[SECURITY] Memory protection change attempt from: " + callerName +
                                   " trying to set executable protection\n";
                OutputDebugStringA(logMsg.c_str());

                SetLastError(ERROR_ACCESS_DENIED);
                return FALSE;
            } else {
                // Allow non-executable protection changes but log them
                std::string logMsg = "[INFO] VirtualProtectEx from: " + callerName +
                                   " protection: 0x" + std::to_string(flNewProtect) + "\n";
                OutputDebugStringA(logMsg.c_str());
            }
        }
    }

    return OriginalVirtualProtectEx(hProcess, lpAddress, dwSize, flNewProtect, lpflOldProtect);
}

// Setup API hooks using Microsoft Detours (since you already have it)
bool SetupMemoryAccessHooks() {
    // Get original function addresses
    HMODULE hKernel32 = GetModuleHandleA("kernel32.dll");
    if (!hKernel32) {
        return false;
    }

    OriginalOpenProcess = (pOpenProcess)GetProcAddress(hKernel32, "OpenProcess");
    OriginalReadProcessMemory = (pReadProcessMemory)GetProcAddress(hKernel32, "ReadProcessMemory");
    OriginalWriteProcessMemory = (pWriteProcessMemory)GetProcAddress(hKernel32, "WriteProcessMemory");
    OriginalVirtualProtectEx = (pVirtualProtectEx)GetProcAddress(hKernel32, "VirtualProtectEx");

    if (!OriginalOpenProcess || !OriginalReadProcessMemory || !OriginalWriteProcessMemory || !OriginalVirtualProtectEx) {
        return false;
    }

    // Install hooks using Detours
    DetourTransactionBegin();
    DetourUpdateThread(GetCurrentThread());

    DetourAttach(&(PVOID&)OriginalOpenProcess, HookedOpenProcess);
    DetourAttach(&(PVOID&)OriginalReadProcessMemory, HookedReadProcessMemory);
    DetourAttach(&(PVOID&)OriginalWriteProcessMemory, HookedWriteProcessMemory);
    DetourAttach(&(PVOID&)OriginalVirtualProtectEx, HookedVirtualProtectEx);

    LONG result = DetourTransactionCommit();

    if (result == NO_ERROR) {
        OutputDebugStringA("[DEBUG] Memory access hooks installed successfully\n");
        return true;
    } else {
        OutputDebugStringA("[ERROR] Failed to install memory access hooks\n");
        return false;
    }
}

// Remove hooks on shutdown
void RemoveMemoryAccessHooks() {
    if (OriginalOpenProcess && OriginalReadProcessMemory && OriginalWriteProcessMemory && OriginalVirtualProtectEx) {
        DetourTransactionBegin();
        DetourUpdateThread(GetCurrentThread());

        DetourDetach(&(PVOID&)OriginalOpenProcess, HookedOpenProcess);
        DetourDetach(&(PVOID&)OriginalReadProcessMemory, HookedReadProcessMemory);
        DetourDetach(&(PVOID&)OriginalWriteProcessMemory, HookedWriteProcessMemory);
        DetourDetach(&(PVOID&)OriginalVirtualProtectEx, HookedVirtualProtectEx);

        DetourTransactionCommit();

        OutputDebugStringA("[DEBUG] Memory access hooks removed\n");
    }
}
